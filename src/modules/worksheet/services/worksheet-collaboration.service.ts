import { Injectable, Logger, ForbiddenException } from '@nestjs/common';
import { RedisService } from '../../redis/redis.service';
import { JwtService } from '@nestjs/jwt';
import { RbacService } from '../../auth/services/rbac.service';
import { 
  IUserPresence, 
  ICollaborationRoom, 
  IRoomSettings, 
  ICollaborationStats,
  ICollaborationError
} from '../interfaces/collaboration.interface';
import { 
  UserAction, 
  RoomStatus, 
  CollaborationErrorCode 
} from '../enums/collaboration-events.enum';
import { UserContext } from './worksheet-question.service';
import { EUserRole } from '../../user/dto/create-user.dto';

@Injectable()
export class WorksheetCollaborationService {
  private readonly logger = new Logger(WorksheetCollaborationService.name);
  private readonly ROOM_PREFIX = 'worksheet:room';
  private readonly PRESENCE_PREFIX = 'worksheet:presence';
  private readonly STATS_PREFIX = 'worksheet:stats';
  private readonly DEFAULT_PRESENCE_TIMEOUT = 30 * 1000; // 30 seconds
  private readonly MAX_ROOM_USERS = 10;

  // In-memory room management for active rooms
  private activeRooms = new Map<string, ICollaborationRoom>();

  constructor(
    private readonly redisService: RedisService,
    private readonly jwtService: JwtService,
    private readonly rbacService: RbacService
  ) {
    // Start cleanup interval for expired presence
    this.startPresenceCleanup();
  }

  /**
   * Validate user access to worksheet and return user context
   */
  async validateWorksheetAccess(
    worksheetId: string,
    token: string
  ): Promise<UserContext> {
    try {
      // Validate JWT token
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET,
      });

      if (!payload.sub || !payload.email || !payload.role) {
        throw new ForbiddenException('Invalid token payload');
      }

      const user: UserContext = {
        sub: payload.sub,
        email: payload.email,
        role: payload.role as EUserRole,
        schoolId: payload.schoolId
      };

      // Note: RBAC validation would be done at the service level
      // For now, we'll do basic validation here
      return user;

    } catch (error) {
      this.logger.error(`Access validation failed for worksheet ${worksheetId}`, error);
      throw new ForbiddenException('Access denied to worksheet');
    }
  }

  /**
   * Add user to collaboration room
   */
  async joinRoom(
    worksheetId: string,
    user: UserContext,
    socketId: string
  ): Promise<ICollaborationRoom> {
    const room = await this.getOrCreateRoom(worksheetId);

    // Check room capacity
    if (room.activeUsers.size >= room.maxUsers) {
      throw new ForbiddenException('Room is at maximum capacity');
    }

    // Check if user is already in room (different socket)
    const existingUser = Array.from(room.activeUsers.values())
      .find(u => u.userId === user.sub);
    
    if (existingUser) {
      // Update socket ID for existing user
      existingUser.socketId = socketId;
      existingUser.lastSeen = new Date();
      existingUser.isActive = true;
    } else {
      // Add new user to room
      const userPresence: IUserPresence = {
        userId: user.sub,
        email: user.email,
        name: user.email, // Could be enhanced with actual name
        role: user.role,
        schoolId: user.schoolId || undefined,
        socketId,
        joinedAt: new Date(),
        lastSeen: new Date(),
        currentAction: UserAction.VIEWING,
        isActive: true
      };

      room.activeUsers.set(socketId, userPresence);
    }

    room.lastActivity = new Date();

    // Persist room state
    await this.persistRoom(room);

    // Update statistics
    await this.updateRoomStats(worksheetId);

    this.logger.log(`User ${user.sub} joined room for worksheet ${worksheetId}`);
    return room;
  }

  /**
   * Remove user from collaboration room
   */
  async leaveRoom(
    worksheetId: string,
    socketId: string
  ): Promise<IUserPresence | null> {
    const room = this.activeRooms.get(worksheetId);
    
    if (!room) {
      return null;
    }

    const user = room.activeUsers.get(socketId);
    
    if (user) {
      room.activeUsers.delete(socketId);
      room.lastActivity = new Date();

      // If room is empty, clean it up
      if (room.activeUsers.size === 0) {
        await this.cleanupRoom(worksheetId);
      } else {
        await this.persistRoom(room);
      }

      // Update statistics
      await this.updateRoomStats(worksheetId);

      this.logger.log(`User ${user.userId} left room for worksheet ${worksheetId}`);
      return user;
    }

    return null;
  }

  /**
   * Update user presence in room
   */
  async updateUserPresence(
    worksheetId: string,
    socketId: string,
    action: UserAction,
    questionId?: string,
    metadata?: any
  ): Promise<IUserPresence | null> {
    const room = this.activeRooms.get(worksheetId);
    
    if (!room) {
      return null;
    }

    const user = room.activeUsers.get(socketId);
    
    if (user) {
      user.currentAction = action;
      user.currentQuestionId = questionId;
      user.lastSeen = new Date();
      user.isActive = true;

      // Update cursor position if provided
      if (metadata?.cursorPosition !== undefined && questionId) {
        user.cursor = {
          questionId,
          position: metadata.cursorPosition
        };
      }

      room.lastActivity = new Date();
      await this.persistRoom(room);

      return user;
    }

    return null;
  }

  /**
   * Get active users in a room
   */
  async getRoomUsers(worksheetId: string): Promise<IUserPresence[]> {
    const room = this.activeRooms.get(worksheetId);
    
    if (!room) {
      return [];
    }

    // Filter out inactive users
    const activeUsers = Array.from(room.activeUsers.values())
      .filter(user => {
        const timeSinceLastSeen = Date.now() - user.lastSeen.getTime();
        return timeSinceLastSeen < this.DEFAULT_PRESENCE_TIMEOUT;
      });

    return activeUsers;
  }

  /**
   * Get room statistics
   */
  async getRoomStats(worksheetId: string): Promise<ICollaborationStats> {
    const redis = this.redisService.getClient();
    const statsKey = `${this.STATS_PREFIX}:${worksheetId}`;

    try {
      const statsData = await redis.get(statsKey);
      
      if (statsData) {
        return JSON.parse(statsData);
      }

      // Generate fresh stats
      return this.generateRoomStats(worksheetId);

    } catch (error) {
      this.logger.error(`Failed to get room stats for ${worksheetId}`, error);
      return this.generateRoomStats(worksheetId);
    }
  }

  /**
   * Update room settings
   */
  async updateRoomSettings(
    worksheetId: string,
    settings: Partial<IRoomSettings>,
    user: UserContext
  ): Promise<IRoomSettings> {
    // Validate admin permissions for certain settings
    if (settings.maxConcurrentEditors || settings.lockTimeout) {
      if (user.role !== EUserRole.ADMIN && user.role !== EUserRole.SCHOOL_MANAGER) {
        throw new ForbiddenException('Insufficient permissions to modify room settings');
      }
    }

    const room = await this.getOrCreateRoom(worksheetId);
    
    room.settings = {
      ...room.settings,
      ...settings
    };

    await this.persistRoom(room);

    this.logger.log(`Room settings updated for worksheet ${worksheetId} by user ${user.sub}`);
    return room.settings;
  }

  /**
   * Check if user is actively editing a question
   */
  async isUserEditingQuestion(
    worksheetId: string,
    questionId: string,
    excludeUserId?: string
  ): Promise<IUserPresence[]> {
    const users = await this.getRoomUsers(worksheetId);
    
    return users.filter(user => 
      user.currentAction === UserAction.EDITING_QUESTION &&
      user.currentQuestionId === questionId &&
      user.userId !== excludeUserId
    );
  }

  /**
   * Get or create collaboration room
   */
  private async getOrCreateRoom(worksheetId: string): Promise<ICollaborationRoom> {
    let room = this.activeRooms.get(worksheetId);

    if (!room) {
      // Try to load from Redis
      room = await this.loadRoom(worksheetId) || undefined;

      if (!room) {
        // Create new room
        room = {
          worksheetId,
          activeUsers: new Map(),
          questionLocks: new Map(),
          editSessions: new Map(),
          conflicts: new Map(),
          roomStatus: RoomStatus.ACTIVE,
          createdAt: new Date(),
          lastActivity: new Date(),
          maxUsers: this.MAX_ROOM_USERS,
          settings: this.getDefaultRoomSettings()
        };
      }

      this.activeRooms.set(worksheetId, room);
    }

    return room;
  }

  /**
   * Load room from Redis
   */
  private async loadRoom(worksheetId: string): Promise<ICollaborationRoom | null> {
    const redis = this.redisService.getClient();
    const roomKey = `${this.ROOM_PREFIX}:${worksheetId}`;

    try {
      const roomData = await redis.get(roomKey);
      
      if (roomData) {
        const data = JSON.parse(roomData);
        
        // Reconstruct Maps from serialized data
        const room: ICollaborationRoom = {
          ...data,
          activeUsers: new Map(data.activeUsers || []),
          questionLocks: new Map(data.questionLocks || []),
          editSessions: new Map(data.editSessions || []),
          conflicts: new Map(data.conflicts || [])
        };

        return room;
      }

    } catch (error) {
      this.logger.error(`Failed to load room ${worksheetId} from Redis`, error);
    }

    return null;
  }

  /**
   * Persist room to Redis
   */
  private async persistRoom(room: ICollaborationRoom): Promise<void> {
    const redis = this.redisService.getClient();
    const roomKey = `${this.ROOM_PREFIX}:${room.worksheetId}`;

    try {
      // Convert Maps to arrays for serialization
      const serializedRoom = {
        ...room,
        activeUsers: Array.from(room.activeUsers.entries()),
        questionLocks: Array.from(room.questionLocks.entries()),
        editSessions: Array.from(room.editSessions.entries()),
        conflicts: Array.from(room.conflicts.entries())
      };

      await redis.setex(
        roomKey,
        3600, // 1 hour expiration
        JSON.stringify(serializedRoom)
      );

    } catch (error) {
      this.logger.error(`Failed to persist room ${room.worksheetId}`, error);
    }
  }

  /**
   * Clean up empty room
   */
  private async cleanupRoom(worksheetId: string): Promise<void> {
    this.activeRooms.delete(worksheetId);
    
    const redis = this.redisService.getClient();
    const roomKey = `${this.ROOM_PREFIX}:${worksheetId}`;
    
    try {
      await redis.del(roomKey);
      this.logger.log(`Cleaned up empty room for worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup room ${worksheetId}`, error);
    }
  }

  /**
   * Generate room statistics
   */
  private async generateRoomStats(worksheetId: string): Promise<ICollaborationStats> {
    const room = this.activeRooms.get(worksheetId);
    
    const stats: ICollaborationStats = {
      worksheetId,
      totalUsers: room?.activeUsers.size || 0,
      activeUsers: room ? await this.getRoomUsers(worksheetId).then(users => users.length) : 0,
      lockedQuestions: room?.questionLocks.size || 0,
      activeEditSessions: room?.editSessions.size || 0,
      unresolvedConflicts: room?.conflicts.size || 0,
      lastActivity: room?.lastActivity || new Date()
    };

    return stats;
  }

  /**
   * Update room statistics in Redis
   */
  private async updateRoomStats(worksheetId: string): Promise<void> {
    const stats = await this.generateRoomStats(worksheetId);
    const redis = this.redisService.getClient();
    const statsKey = `${this.STATS_PREFIX}:${worksheetId}`;

    try {
      await redis.setex(
        statsKey,
        300, // 5 minutes expiration
        JSON.stringify(stats)
      );
    } catch (error) {
      this.logger.error(`Failed to update room stats for ${worksheetId}`, error);
    }
  }

  /**
   * Get default room settings
   */
  private getDefaultRoomSettings(): IRoomSettings {
    return {
      lockTimeout: 5 * 60 * 1000, // 5 minutes
      presenceTimeout: 30 * 1000, // 30 seconds
      maxConcurrentEditors: 5,
      enableTypingIndicators: true,
      enableAutoSave: true,
      autoSaveInterval: 10 * 1000, // 10 seconds
      conflictResolutionStrategy: 'last_writer_wins' as any,
      allowGuestUsers: false
    };
  }

  /**
   * Start periodic cleanup of expired presence
   */
  private startPresenceCleanup(): void {
    setInterval(async () => {
      for (const [worksheetId, room] of this.activeRooms.entries()) {
        let hasChanges = false;
        
        for (const [socketId, user] of room.activeUsers.entries()) {
          const timeSinceLastSeen = Date.now() - user.lastSeen.getTime();
          
          if (timeSinceLastSeen > this.DEFAULT_PRESENCE_TIMEOUT) {
            room.activeUsers.delete(socketId);
            hasChanges = true;
            this.logger.log(`Removed inactive user ${user.userId} from room ${worksheetId}`);
          }
        }

        if (hasChanges) {
          if (room.activeUsers.size === 0) {
            await this.cleanupRoom(worksheetId);
          } else {
            await this.persistRoom(room);
          }
        }
      }
    }, 60000); // Run every minute
  }
}
